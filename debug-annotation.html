<!DOCTYPE html>
<html>
<head>
    <title>Debug Annotation System</title>
    <link rel="stylesheet" href="/min/?g=ebookshelf-css">
    <script type="text/javascript" src="/min/?g=ebookshelf-js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .status {
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #test-container {
            width: 400px;
            height: 300px;
            border: 2px solid #333;
            margin: 20px 0;
            background-color: white;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Debug Annotation System</h1>
    
    <div class="debug-section">
        <h2>1. Library Loading Status</h2>
        <div id="library-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. Global Variables Check</h2>
        <div id="globals-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. PageAnnotator Component Test</h2>
        <div id="component-status"></div>
        <div id="test-container"></div>
        <button onclick="testPageAnnotator()">Test PageAnnotator Component</button>
        <button onclick="clearTest()">Clear Test</button>
    </div>
    
    <div class="debug-section">
        <h2>4. Console Logs</h2>
        <div id="console-logs" class="code"></div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const logs = [];
        
        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString()});
            updateConsoleLogs();
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            updateConsoleLogs();
            originalError.apply(console, args);
        };
        
        function updateConsoleLogs() {
            const logsDiv = document.getElementById('console-logs');
            logsDiv.innerHTML = logs.slice(-10).map(log => 
                `[${log.time}] ${log.type.toUpperCase()}: ${log.message}`
            ).join('\n');
        }
        
        function checkLibraries() {
            const statusDiv = document.getElementById('library-status');
            let html = '';
            
            const checks = [
                { name: 'jQuery ($)', check: () => typeof $ !== 'undefined' },
                { name: 'React', check: () => typeof React !== 'undefined' },
                { name: 'ReactDOM', check: () => typeof ReactDOM !== 'undefined' },
                { name: 'LiterallyCanvas (LC)', check: () => typeof LC !== 'undefined' },
                { name: 'LC.tools', check: () => typeof LC !== 'undefined' && typeof LC.tools !== 'undefined' },
                { name: 'LC.LiterallyCanvasReactComponent', check: () => typeof LC !== 'undefined' && typeof LC.LiterallyCanvasReactComponent !== 'undefined' },
                { name: 'PageAnnotator', check: () => typeof PageAnnotator !== 'undefined' }
            ];
            
            checks.forEach(check => {
                const status = check.check() ? 'success' : 'error';
                const symbol = check.check() ? '✓' : '✗';
                html += `<div class="status ${status}">${symbol} ${check.name}</div>`;
            });
            
            statusDiv.innerHTML = html;
        }
        
        function checkGlobals() {
            const statusDiv = document.getElementById('globals-status');
            let html = '';
            
            // Check window object properties
            const globalChecks = [
                'React', 'ReactDOM', 'LC', 'PageAnnotator', '$', 'jQuery'
            ];
            
            globalChecks.forEach(name => {
                const exists = window[name] !== undefined;
                const status = exists ? 'success' : 'warning';
                const symbol = exists ? '✓' : '?';
                const type = exists ? typeof window[name] : 'undefined';
                html += `<div class="status ${status}">${symbol} window.${name}: ${type}</div>`;
            });
            
            statusDiv.innerHTML = html;
        }
        
        function testPageAnnotator() {
            const statusDiv = document.getElementById('component-status');
            const container = document.getElementById('test-container');
            
            try {
                console.log('Starting PageAnnotator test...');
                
                if (typeof PageAnnotator === 'undefined') {
                    throw new Error('PageAnnotator is not available');
                }
                
                if (typeof React === 'undefined') {
                    throw new Error('React is not available');
                }
                
                if (typeof ReactDOM === 'undefined') {
                    throw new Error('ReactDOM is not available');
                }
                
                if (typeof LC === 'undefined') {
                    throw new Error('LiterallyCanvas (LC) is not available');
                }
                
                if (typeof LC.LiterallyCanvasReactComponent === 'undefined') {
                    throw new Error('LC.LiterallyCanvasReactComponent is not available');
                }
                
                const props = {
                    width: 400,
                    height: 300,
                    tools: [LC.tools.Pencil, LC.tools.Eraser],
                    primaryColor: '#ff0000',
                    onInit: function(lc) {
                        console.log('PageAnnotator initialized successfully:', lc);
                    }
                };
                
                console.log('Rendering PageAnnotator with props:', props);
                ReactDOM.render(React.createElement(PageAnnotator, props), container);
                
                statusDiv.innerHTML = '<div class="status success">✓ PageAnnotator rendered successfully</div>';
                console.log('PageAnnotator test completed successfully');
                
            } catch (error) {
                console.error('PageAnnotator test failed:', error);
                statusDiv.innerHTML = `<div class="status error">✗ PageAnnotator test failed: ${error.message}</div>`;
            }
        }
        
        function clearTest() {
            const container = document.getElementById('test-container');
            const statusDiv = document.getElementById('component-status');
            
            ReactDOM.unmountComponentAtNode(container);
            container.innerHTML = '';
            statusDiv.innerHTML = '<div class="status warning">Test cleared</div>';
            console.log('Test cleared');
        }
        
        // Run checks when page loads
        setTimeout(() => {
            console.log('Running initial checks...');
            checkLibraries();
            checkGlobals();
        }, 1000); // Wait for scripts to load
    </script>
</body>
</html>
