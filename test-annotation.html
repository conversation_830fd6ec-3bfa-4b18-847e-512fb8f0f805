<!DOCTYPE html>
<html>
<head>
    <title>Annotation Test</title>
    <link rel="stylesheet" href="/min/?g=ebookshelf-css">
    <script type="text/javascript" src="/min/?g=ebookshelf-js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        #test-container {
            width: 400px;
            height: 300px;
            border: 2px solid #333;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Annotation System Test</h1>
    
    <div class="test-section">
        <h2>Library Loading Test</h2>
        <div id="library-status"></div>
    </div>
    
    <div class="test-section">
        <h2>React Component Test</h2>
        <div id="component-status"></div>
        <div id="test-container"></div>
        <button onclick="testPageAnnotator()">Test PageAnnotator</button>
    </div>

    <script>
        function checkLibraries() {
            const statusDiv = document.getElementById('library-status');
            let html = '';
            
            const checks = [
                { name: 'jQuery', check: () => typeof $ !== 'undefined' },
                { name: 'React', check: () => typeof React !== 'undefined' },
                { name: 'ReactDOM', check: () => typeof ReactDOM !== 'undefined' },
                { name: 'LiterallyCanvas (LC)', check: () => typeof LC !== 'undefined' },
                { name: 'LC.tools', check: () => typeof LC !== 'undefined' && typeof LC.tools !== 'undefined' },
                { name: 'LC.LiterallyCanvasReactComponent', check: () => typeof LC !== 'undefined' && typeof LC.LiterallyCanvasReactComponent !== 'undefined' },
                { name: 'PageAnnotator', check: () => typeof PageAnnotator !== 'undefined' }
            ];
            
            checks.forEach(test => {
                const passed = test.check();
                const className = passed ? 'success' : 'error';
                const status = passed ? '✓ Loaded' : '✗ Missing';
                html += `<div class="status ${className}">${test.name}: ${status}</div>`;
            });
            
            statusDiv.innerHTML = html;
        }
        
        function testPageAnnotator() {
            const statusDiv = document.getElementById('component-status');
            const container = document.getElementById('test-container');
            
            try {
                if (typeof PageAnnotator === 'undefined') {
                    throw new Error('PageAnnotator is not available');
                }
                
                const props = {
                    width: 400,
                    height: 300,
                    tools: [LC.tools.Pencil, LC.tools.Eraser],
                    primaryColor: '#ff0000',
                    onInit: function(lc) {
                        console.log('PageAnnotator initialized:', lc);
                    }
                };
                
                ReactDOM.render(React.createElement(PageAnnotator, props), container);
                statusDiv.innerHTML = '<div class="status success">✓ PageAnnotator rendered successfully</div>';
                
            } catch (error) {
                console.error('PageAnnotator test failed:', error);
                statusDiv.innerHTML = `<div class="status error">✗ PageAnnotator test failed: ${error.message}</div>`;
            }
        }
        
        // Run tests when page loads
        $(document).ready(function() {
            setTimeout(checkLibraries, 1000); // Wait a bit for all scripts to load
        });
    </script>
</body>
</html>
