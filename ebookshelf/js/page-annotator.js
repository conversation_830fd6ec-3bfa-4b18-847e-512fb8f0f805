// PageAnnotator React Component - Transpiled from JSX
// This component wraps LiterallyCanvas core functionality in a React component
// Browser-compatible version that uses global React and ReactDOM

"use strict";

// Use global React instead of require
var _react = { default: window.React };
// PageAnnotator React Component
// This component uses the LiterallyCanvasReactComponent with built-in GUI
var PageAnnotator = function PageAnnotator(_ref) {
  var _ref$width = _ref.width,
    width = _ref$width === void 0 ? 800 : _ref$width,
    _ref$height = _ref.height,
    height = _ref$height === void 0 ? 600 : _ref$height,
    _ref$tools = _ref.tools,
    tools = _ref$tools === void 0 ? null : _ref$tools,
    _ref$onDrawingChange = _ref.onDrawingChange,
    onDrawingChange = _ref$onDrawingChange === void 0 ? null : _ref$onDrawingChange,
    _ref$snapshot = _ref.snapshot,
    snapshot = _ref$snapshot === void 0 ? null : _ref$snapshot,
    _ref$primaryColor = _ref.primaryColor,
    primaryColor = _ref$primaryColor === void 0 ? '#000' : _ref$primaryColor,
    _ref$secondaryColor = _ref.secondaryColor,
    secondaryColor = _ref$secondaryColor === void 0 ? '#fff' : _ref$secondaryColor,
    _ref$backgroundColor = _ref.backgroundColor,
    backgroundColor = _ref$backgroundColor === void 0 ? 'transparent' : _ref$backgroundColor,
    _ref$strokeWidths = _ref.strokeWidths,
    strokeWidths = _ref$strokeWidths === void 0 ? [1, 2, 5, 10, 20] : _ref$strokeWidths,
    _ref$defaultStrokeWid = _ref.defaultStrokeWidth,
    defaultStrokeWidth = _ref$defaultStrokeWid === void 0 ? 2 : _ref$defaultStrokeWid,
    _ref$imageSize = _ref.imageSize,
    imageSize = _ref$imageSize === void 0 ? null : _ref$imageSize,
    _ref$toolbarPosition = _ref.toolbarPosition,
    toolbarPosition = _ref$toolbarPosition === void 0 ? 'top' : _ref$toolbarPosition,
    _ref$onInit = _ref.onInit,
    onInit = _ref$onInit === void 0 ? null : _ref$onInit;
  // Check if required dependencies are available
  if (typeof window.LC === 'undefined') {
    console.error('LiterallyCanvas (LC) is not available');
    return /*#__PURE__*/_react.default.createElement('div', {
      style: {
        color: 'red',
        padding: '20px'
      }
    }, 'Error: LiterallyCanvas library not loaded');
  }
  if (typeof window.LC.LiterallyCanvasReactComponent === 'undefined') {
    console.error('LiterallyCanvasReactComponent is not available');
    return /*#__PURE__*/_react.default.createElement('div', {
      style: {
        color: 'red',
        padding: '20px'
      }
    }, 'Error: LiterallyCanvasReactComponent not available');
  }

  // Use the React GUI component from LiterallyCanvas
  var _window$LC = window.LC,
    LiterallyCanvasReactComponent = _window$LC.LiterallyCanvasReactComponent,
    lcTools = _window$LC.tools;

  // Default tools if none provided
  var defaultTools = [lcTools.Pencil, lcTools.Eraser, lcTools.Line, lcTools.Pan];
  return /*#__PURE__*/_react.default.createElement(LiterallyCanvasReactComponent, {
    imageSize: imageSize || {
      width: width,
      height: height
    },
    tools: tools || defaultTools,
    toolbarPosition: toolbarPosition,
    snapshot: snapshot,
    onDrawingChange: onDrawingChange,
    onInit: onInit,
    primaryColor: primaryColor,
    secondaryColor: secondaryColor,
    backgroundColor: backgroundColor,
    strokeWidths: strokeWidths,
    defaultStrokeWidth: defaultStrokeWidth
  });
};

// Make available globally for browser use
if (typeof window !== 'undefined') {
  window.PageAnnotator = PageAnnotator;
}

// Also support CommonJS for testing environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PageAnnotator;
}
