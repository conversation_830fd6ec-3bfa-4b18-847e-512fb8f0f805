import React from 'react';

// PageAnnotator React Component
// This component uses the LiterallyCanvasReactComponent with built-in GUI
const PageAnnotator = ({
  width = 800,
  height = 600,
  tools = null,
  onDrawingChange = null,
  snapshot = null,
  primaryColor = '#000',
  secondaryColor = '#fff',
  backgroundColor = 'transparent',
  strokeWidths = [1, 2, 5, 10, 20],
  defaultStrokeWidth = 2,
  imageSize = null,
  toolbarPosition = 'top',
  onInit = null
}) => {
  // Check if required dependencies are available
  if (typeof window.LC === 'undefined') {
    console.error('LiterallyCanvas (LC) is not available');
    return React.createElement('div', { style: { color: 'red', padding: '20px' } }, 'Error: LiterallyCanvas library not loaded');
  }

  if (typeof window.LC.LiterallyCanvasReactComponent === 'undefined') {
    console.error('LiterallyCanvasReactComponent is not available');
    return React.createElement('div', { style: { color: 'red', padding: '20px' } }, 'Error: LiterallyCanvasReactComponent not available');
  }

  // Use the React GUI component from LiterallyCanvas
  const { LiterallyCanvasReactComponent, tools: lcTools } = window.LC;

  // Default tools if none provided
  const defaultTools = [
    lcTools.Pencil,
    lcTools.Eraser,
    lcTools.Line,
    lcTools.Pan
  ];

  return React.createElement(LiterallyCanvasReactComponent, {
    imageSize: imageSize || { width, height },
    tools: tools || defaultTools,
    toolbarPosition: toolbarPosition,
    snapshot: snapshot,
    onDrawingChange: onDrawingChange,
    onInit: onInit,
    primaryColor: primaryColor,
    secondaryColor: secondaryColor,
    backgroundColor: backgroundColor,
    strokeWidths: strokeWidths,
    defaultStrokeWidth: defaultStrokeWidth
  });
};

export default PageAnnotator;
