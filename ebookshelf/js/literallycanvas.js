// LiterallyCanvas with React Support
// This file combines the core LiterallyCanvas functionality with React components

// First, load the core LiterallyCanvas library
// The core library is already loaded via literallycanvas-core.js

// Extend LC with React components
(function() {
  'use strict';
  
  // Ensure LC is available
  if (typeof LC === 'undefined') {
    console.error('LiterallyCanvas core library not found. Make sure literallycanvas-core.js is loaded first.');
    return;
  }

  // React Component for LiterallyCanvas
  const LiterallyCanvasReact = (props) => {
    const {
      width = 800,
      height = 600,
      tools = null,
      onDrawingChange = null,
      snapshot = null,
      backgroundShapes = [],
      primaryColor = '#000',
      secondaryColor = '#fff',
      backgroundColor = 'transparent',
      strokeWidths = [1, 2, 5, 10, 20],
      defaultStrokeWidth = 2,
      imageSize = null,
      toolbarPosition = 'top',
      onInit = null,
      className = '',
      style = {}
    } = props;

    const containerRef = React.useRef(null);
    const lcRef = React.useRef(null);
    const [isInitialized, setIsInitialized] = React.useState(false);

    // Default tools if none provided
    const defaultTools = [
      LC.tools.Pencil,
      LC.tools.Eraser,
      LC.tools.Line,
      LC.tools.Rectangle,
      LC.tools.Ellipse,
      LC.tools.Text,
      LC.tools.Polygon,
      LC.tools.Pan,
      LC.tools.Eyedropper
    ];

    React.useEffect(() => {
      if (!containerRef.current || isInitialized) return;

      // Initialize LiterallyCanvas
      const lcOptions = {
        imageSize: imageSize || { width: width, height: height },
        tools: tools || defaultTools,
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
        backgroundColor: backgroundColor,
        strokeWidths: strokeWidths,
        defaultStrokeWidth: defaultStrokeWidth,
        backgroundShapes: backgroundShapes,
        snapshot: snapshot,
        toolbarPosition: toolbarPosition
      };

      // Create LiterallyCanvas instance
      const lc = LC.init(containerRef.current, lcOptions);
      lcRef.current = lc;

      // Set up event listeners
      if (onDrawingChange) {
        lc.on('drawingChange', onDrawingChange);
      }

      if (onInit) {
        onInit(lc);
      }

      setIsInitialized(true);

      // Cleanup function
      return () => {
        if (lcRef.current) {
          lcRef.current.teardown();
          lcRef.current = null;
        }
        setIsInitialized(false);
      };
    }, []);

    // Update colors when props change
    React.useEffect(() => {
      if (lcRef.current) {
        lcRef.current.setColor('primary', primaryColor);
      }
    }, [primaryColor]);

    React.useEffect(() => {
      if (lcRef.current) {
        lcRef.current.setColor('secondary', secondaryColor);
      }
    }, [secondaryColor]);

    React.useEffect(() => {
      if (lcRef.current) {
        lcRef.current.setColor('background', backgroundColor);
      }
    }, [backgroundColor]);

    // Expose LC instance methods
    const getSnapshot = () => {
      return lcRef.current ? lcRef.current.getSnapshot() : null;
    };

    const loadSnapshot = (snapshot) => {
      if (lcRef.current) {
        lcRef.current.loadSnapshot(snapshot);
      }
    };

    const clear = () => {
      if (lcRef.current) {
        lcRef.current.clear();
      }
    };

    const undo = () => {
      if (lcRef.current) {
        lcRef.current.undo();
      }
    };

    const redo = () => {
      if (lcRef.current) {
        lcRef.current.redo();
      }
    };

    const setTool = (tool) => {
      if (lcRef.current) {
        lcRef.current.setTool(new tool(lcRef.current));
      }
    };

    const setZoom = (scale) => {
      if (lcRef.current) {
        lcRef.current.setZoom(scale);
      }
    };

    const setPan = (x, y) => {
      if (lcRef.current) {
        lcRef.current.setPan(x, y);
      }
    };

    const getImage = (options = {}) => {
      return lcRef.current ? lcRef.current.getImage(options) : null;
    };

    const getSVGString = () => {
      return lcRef.current ? lcRef.current.getSVGString() : '';
    };

    // Expose methods via ref
    React.useImperativeHandle(React.forwardRef(() => {}), () => ({
      getSnapshot,
      loadSnapshot,
      clear,
      undo,
      redo,
      setTool,
      setZoom,
      setPan,
      getImage,
      getSVGString,
      lc: lcRef.current
    }));

    const containerStyle = {
      width: width + 'px',
      height: height + 'px',
      border: '1px solid #ccc',
      position: 'relative',
      ...style
    };

    return React.createElement('div', {
      ref: containerRef,
      style: containerStyle,
      className: 'literally-canvas-container ' + className
    });
  };

  // React GUI Component with built-in toolbar
  const LiterallyCanvasReactComponent = (props) => {
    const {
      imageSize = { width: 800, height: 600 },
      tools = [LC.tools.Pencil, LC.tools.Eraser, LC.tools.Line, LC.tools.Pan],
      toolbarPosition = 'top',
      snapshot = null,
      onDrawingChange = null,
      onInit = null,
      primaryColor = '#000',
      secondaryColor = '#fff',
      backgroundColor = 'transparent',
      strokeWidths = [1, 2, 5, 10, 20],
      defaultStrokeWidth = 2
    } = props;

    const containerRef = React.useRef(null);
    const lcRef = React.useRef(null);
    const [isInitialized, setIsInitialized] = React.useState(false);
    const [selectedTool, setSelectedTool] = React.useState(0);

    React.useEffect(() => {
      if (!containerRef.current || isInitialized) return;

      // Initialize LiterallyCanvas
      const lcOptions = {
        imageSize: imageSize,
        tools: tools,
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
        backgroundColor: backgroundColor,
        strokeWidths: strokeWidths,
        defaultStrokeWidth: defaultStrokeWidth,
        snapshot: snapshot
      };

      // Create LiterallyCanvas instance
      const lc = LC.init(containerRef.current, lcOptions);
      lcRef.current = lc;

      // Set default tool
      if (tools.length > 0) {
        lc.setTool(new tools[0](lc));
      }

      // Set up event listeners
      if (onDrawingChange) {
        lc.on('drawingChange', onDrawingChange);
      }

      if (onInit) {
        onInit(lc);
      }

      setIsInitialized(true);

      // Cleanup function
      return () => {
        if (lcRef.current) {
          lcRef.current.teardown();
          lcRef.current = null;
        }
        setIsInitialized(false);
      };
    }, []);

    const handleToolSelect = (toolIndex) => {
      if (lcRef.current && tools[toolIndex]) {
        lcRef.current.setTool(new tools[toolIndex](lcRef.current));
        setSelectedTool(toolIndex);
      }
    };

    const handleUndo = () => {
      if (lcRef.current) {
        lcRef.current.undo();
      }
    };

    const handleRedo = () => {
      if (lcRef.current) {
        lcRef.current.redo();
      }
    };

    const handleZoomIn = () => {
      if (lcRef.current) {
        lcRef.current.zoom(1.2);
      }
    };

    const handleZoomOut = () => {
      if (lcRef.current) {
        lcRef.current.zoom(0.8);
      }
    };

    const getToolName = (tool) => {
      return tool.name || tool.constructor.name || 'Tool';
    };

    const toolbarStyle = {
      display: 'flex',
      flexDirection: toolbarPosition === 'top' || toolbarPosition === 'bottom' ? 'row' : 'column',
      gap: '5px',
      padding: '10px',
      backgroundColor: '#f0f0f0',
      borderRadius: '4px',
      marginBottom: toolbarPosition === 'top' ? '10px' : '0',
      marginTop: toolbarPosition === 'bottom' ? '10px' : '0'
    };

    const buttonStyle = {
      padding: '8px 12px',
      border: '1px solid #ccc',
      backgroundColor: '#fff',
      borderRadius: '4px',
      cursor: 'pointer',
      fontSize: '12px'
    };

    const selectedButtonStyle = {
      ...buttonStyle,
      backgroundColor: '#007cba',
      color: '#fff'
    };

    const canvasStyle = {
      width: imageSize.width + 'px',
      height: imageSize.height + 'px',
      border: '1px solid #ccc',
      position: 'relative'
    };

    const containerStyle = {
      display: 'flex',
      flexDirection: toolbarPosition === 'top' || toolbarPosition === 'bottom' ? 'column' : 'row',
      alignItems: 'flex-start'
    };

    const toolbar = React.createElement('div', { style: toolbarStyle }, [
      // Tool buttons
      ...tools.map((tool, index) =>
        React.createElement('button', {
          key: `tool-${index}`,
          style: selectedTool === index ? selectedButtonStyle : buttonStyle,
          onClick: () => handleToolSelect(index),
          title: getToolName(tool)
        }, getToolName(tool))
      ),
      // Separator
      React.createElement('div', { key: 'separator1', style: { width: '1px', height: '20px', backgroundColor: '#ccc', margin: '0 5px' } }),
      // Undo/Redo buttons
      React.createElement('button', {
        key: 'undo',
        style: buttonStyle,
        onClick: handleUndo,
        title: 'Undo'
      }, 'Undo'),
      React.createElement('button', {
        key: 'redo',
        style: buttonStyle,
        onClick: handleRedo,
        title: 'Redo'
      }, 'Redo'),
      // Separator
      React.createElement('div', { key: 'separator2', style: { width: '1px', height: '20px', backgroundColor: '#ccc', margin: '0 5px' } }),
      // Zoom buttons
      React.createElement('button', {
        key: 'zoom-in',
        style: buttonStyle,
        onClick: handleZoomIn,
        title: 'Zoom In'
      }, 'Zoom In'),
      React.createElement('button', {
        key: 'zoom-out',
        style: buttonStyle,
        onClick: handleZoomOut,
        title: 'Zoom Out'
      }, 'Zoom Out')
    ]);

    const canvas = React.createElement('div', {
      ref: containerRef,
      style: canvasStyle,
      className: 'literally-canvas-container'
    });

    const content = toolbarPosition === 'top' ? [toolbar, canvas] :
                   toolbarPosition === 'bottom' ? [canvas, toolbar] :
                   [toolbar, canvas];

    return React.createElement('div', { style: containerStyle }, content);
  };

  // Add React components to LC namespace
  LC.LiterallyCanvasReact = LiterallyCanvasReact;
  LC.LiterallyCanvasReactComponent = LiterallyCanvasReactComponent;

  // Also make them available as standalone components
  if (typeof window !== 'undefined') {
    window.LiterallyCanvasReact = LiterallyCanvasReact;
    window.LiterallyCanvasReactComponent = LiterallyCanvasReactComponent;
  }

  // Helper function to create shapes
  LC.createShape = LC.createShape || function(shapeType, options) {
    return LC.shapes.createShape(shapeType, options);
  };

})();
