<!DOCTYPE html>
<html>
<head>
    <title>Simple PageAnnotator Test</title>
    <link rel="stylesheet" href="/min/?g=ebookshelf-css">
    <script type="text/javascript" src="/min/?g=ebookshelf-js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        #test-container {
            width: 400px;
            height: 300px;
            border: 2px solid #333;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Simple PageAnnotator Test</h1>
    
    <div id="status"></div>
    <div id="test-container"></div>
    <button onclick="testPageAnnotator()">Test PageAnnotator</button>

    <script>
        function checkDependencies() {
            const statusDiv = document.getElementById('status');
            let html = '<h3>Dependency Check:</h3>';
            
            const checks = [
                { name: 'React', check: () => typeof React !== 'undefined' },
                { name: 'ReactDOM', check: () => typeof ReactDOM !== 'undefined' },
                { name: 'LiterallyCanvas (LC)', check: () => typeof LC !== 'undefined' },
                { name: 'LC.tools', check: () => typeof LC !== 'undefined' && typeof LC.tools !== 'undefined' },
                { name: 'LC.LiterallyCanvasReactComponent', check: () => typeof LC !== 'undefined' && typeof LC.LiterallyCanvasReactComponent !== 'undefined' },
                { name: 'PageAnnotator', check: () => typeof PageAnnotator !== 'undefined' }
            ];
            
            checks.forEach(check => {
                const status = check.check() ? 'success' : 'error';
                const symbol = check.check() ? '✓' : '✗';
                html += `<div class="status ${status}">${symbol} ${check.name}</div>`;
            });
            
            statusDiv.innerHTML = html;
        }
        
        function testPageAnnotator() {
            const container = document.getElementById('test-container');
            
            try {
                if (typeof PageAnnotator === 'undefined') {
                    throw new Error('PageAnnotator is not available');
                }
                
                const props = {
                    width: 400,
                    height: 300,
                    tools: [LC.tools.Pencil, LC.tools.Eraser],
                    primaryColor: '#ff0000',
                    onInit: function(lc) {
                        console.log('PageAnnotator initialized:', lc);
                    }
                };
                
                ReactDOM.render(React.createElement(PageAnnotator, props), container);
                console.log('PageAnnotator rendered successfully');
                
            } catch (error) {
                console.error('PageAnnotator test failed:', error);
                container.innerHTML = `<div class="status error">✗ PageAnnotator test failed: ${error.message}</div>`;
            }
        }
        
        // Run dependency check when page loads
        setTimeout(checkDependencies, 1000); // Wait a bit for all scripts to load
    </script>
</body>
</html>
