#!/usr/bin/env node

/**
 * Simple script to compile PageAnnotator.jsx to page-annotator.js
 * This script uses Babe<PERSON> to transpile JSX to regular JavaScript
 * 
 * Usage: node compile-page-annotator.js
 * 
 * Requirements:
 * - @babel/core
 * - @babel/preset-react
 * - @babel/preset-env
 * 
 * Install with: npm install --save-dev @babel/core @babel/preset-react @babel/preset-env
 */

const fs = require('fs');
const path = require('path');

// Try to use Babel if available
let babel;
try {
  babel = require('@babel/core');
} catch (e) {
  console.error('Babel not found. Please install @babel/core, @babel/preset-react, and @babel/preset-env');
  console.error('Run: npm install --save-dev @babel/core @babel/preset-react @babel/preset-env');
  process.exit(1);
}

const inputFile = path.join(__dirname, 'ebookshelf/js/PageAnnotator.jsx');
const outputFile = path.join(__dirname, 'ebookshelf/js/page-annotator.js');

// Read the JSX file
const jsxContent = fs.readFileSync(inputFile, 'utf8');

// Babel configuration
const babelOptions = {
  presets: [
    ['@babel/preset-env', {
      targets: {
        browsers: ['> 1%', 'last 2 versions', 'ie >= 11']
      }
    }],
    '@babel/preset-react'
  ],
  plugins: []
};

// Transform the code
try {
  const result = babel.transformSync(jsxContent, babelOptions);
  
  // Create browser-compatible output that uses global React
  const browserCompatibleCode = result.code
    // Replace require("react") with global React
    .replace(/var _react = _interopRequireDefault\(require\("react"\)\);/g, 'var _react = { default: window.React };')
    .replace(/require\("react"\)/g, 'window.React')
    // Remove the _interopRequireDefault function since we don't need it
    .replace(/function _interopRequireDefault\(e\) \{ return e && e\.__esModule \? e : \{ default: e \}; \}/g, '')
    // Remove CommonJS exports
    .replace(/Object\.defineProperty\(exports, "__esModule", \{\s*value: true\s*\}\);/g, '')
    .replace(/exports\.default = void 0;/g, '')
    .replace(/var _default = exports\.default = PageAnnotator;/g, '');

  const output = `// PageAnnotator React Component - Transpiled from JSX
// This component wraps LiterallyCanvas core functionality in a React component
// Browser-compatible version that uses global React and ReactDOM

${browserCompatibleCode}

// Make available globally for browser use
if (typeof window !== 'undefined') {
  window.PageAnnotator = PageAnnotator;
}

// Also support CommonJS for testing environments
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PageAnnotator;
}
`;

  // Write the compiled file
  fs.writeFileSync(outputFile, output);
  console.log(`✅ Successfully compiled ${inputFile} to ${outputFile}`);
  
} catch (error) {
  console.error('❌ Compilation failed:', error.message);
  process.exit(1);
}
